"use client"

import type React from "react"
import { useState, useEffect, useRef, use<PERSON><PERSON>back, useReducer } from "react"
import { useF<PERSON>, <PERSON> } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Check,
  Settings2,
  X,
  ChevronLeft,
  CreditCard,
  Smartphone,
  Smile,
  CalendarDays,
  Clock,
  Sparkles,
  Eye,
  Upload,
  XCircle,
  ThumbsDown,
  FileText,
  MapPin,
  Building,
  Calendar,
  DollarSign,
  MessageCircle,
  Shield,
  Star,
  ArrowRight,
  CheckCircle,
  Rocket,
  CheckCircle2,
  Phone,
  TrendingUp,
  RefreshCw,
  Award,
  Verified,
  BadgeCheck,
  ShieldCheck,
  Zap,
  Lock,
  Plus,
} from "lucide-react"
import Image from "next/image"

export default function LocPayPage() {
  // --- Types and Interfaces ---
  interface FormData {
    nomeCompleto: string
    cpf: string
    whatsapp: string
    valorAluguelLiquido: string
    mesesAntecipacao: string
    imobiliaria: string
    contratoFileName: string
    jaEhCliente: boolean
    dataConsent: boolean
  }

  interface ExtractedData {
    nomeCompleto: string
    celular: string
    email: string
    Inquilino: string
    Proprietário: string
    Imóvel: string
    "Valor do Aluguel": string
    "Vigência do Contrato": string
    Imobiliária: string
  }

  interface AppState {
    step: number
    loading: boolean
    loadingMsg: string
    formData: FormData
    proposalMonths: number
    error: string
    success: string
    extractedData: ExtractedData | null
    showRejectDialog: boolean
    documentFileName: string
    authStep: "cpf" | "registration" | "verification" | "complete"
    isNewUser: boolean
    verificationCode: string
    authToken: string | null
    user: any | null
  }

  type Action =
    | { type: "SET_STEP"; payload: number }
    | { type: "SET_LOADING"; payload: { loading: boolean; msg: string } }
    | { type: "SET_FORM_DATA"; payload: Partial<FormData> }
    | { type: "SET_EXTRACTED_DATA"; payload: ExtractedData }
    | { type: "SET_ERROR"; payload: string }
    | { type: "SET_SUCCESS"; payload: string }
    | { type: "SHOW_REJECT_DIALOG"; payload: boolean }
    | { type: "SET_DOCUMENT"; payload: string }
    | { type: "SET_AUTH_STEP"; payload: "cpf" | "registration" | "verification" | "complete" }
    | { type: "SET_IS_NEW_USER"; payload: boolean }
    | { type: "SET_VERIFICATION_CODE"; payload: string }
    | { type: "SET_AUTH_TOKEN"; payload: string | null }
    | { type: "SET_USER"; payload: any | null }
    | { type: "RESET_STATE" }

  // --- Initial State ---
  const INITIAL_STATE: AppState = {
    step: 0,
    loading: false,
    loadingMsg: "",
    formData: {
      nomeCompleto: "",
      cpf: "",
      whatsapp: "",
      valorAluguelLiquido: "",
      mesesAntecipacao: "",
      imobiliaria: "",
      contratoFileName: "",
      jaEhCliente: false,
      dataConsent: false,
    },
    proposalMonths: 6,
    error: "",
    success: "",
    extractedData: null,
    showRejectDialog: false,
    documentFileName: "",
    authStep: "cpf",
    isNewUser: false,
    verificationCode: "",
    authToken: null,
    user: null,
  }

  // --- Mock Data ---
  const MOCK_EXTRACTED_DATA: ExtractedData = {
    nomeCompleto: "João da Silva Santos",
    celular: "(11) 99999-9999",
    email: "<EMAIL>",
    Inquilino: "João da Silva Santos",
    Proprietário: "Maria Oliveira Costa",
    Imóvel: "Rua das Palmeiras, 456, São Paulo/SP",
    "Valor do Aluguel": "R$ 2.800,00",
    "Vigência do Contrato": "01/01/2024 - 31/12/2026",
    Imobiliária: "Imobiliária Alfa Ltda",
  }

  // --- Zod Schemas ---
  const cpfSchema = z.object({
    cpf: z.string()
      .min(11, "CPF deve ter 11 dígitos")
      .max(14, "CPF inválido")
      .refine((cpf) => validateCPF(cpf), "CPF inválido"),
  })

  const registrationSchema = z.object({
    nomeCompleto: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
    cpf: z.string()
      .min(11, "CPF deve ter 11 dígitos")
      .max(14, "CPF inválido")
      .refine((cpf: string) => validateCPF(cpf), "CPF inválido"),
    whatsapp: z.string().min(10, "WhatsApp deve ter pelo menos 10 dígitos"),
  })

  const verificationSchema = z.object({
    code: z.string().length(6, "Código deve ter 6 dígitos"),
  })

  const firstPageSchema = z.object({
    nomeCompleto: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
    cpf: z.string()
      .min(11, "CPF deve ter 11 dígitos")
      .max(14, "CPF inválido")
      .refine((cpf: string) => validateCPF(cpf), "CPF inválido"),
    whatsapp: z.string().min(10, "WhatsApp deve ter pelo menos 10 dígitos"),
    jaEhCliente: z.boolean(),
  })

  const secondPageSchema = z.object({
    valorAluguelLiquido: z.string().min(1, "Valor do aluguel é obrigatório"),
    mesesAntecipacao: z.string().min(1, "Número de meses é obrigatório"),
    imobiliaria: z.string().min(1, "Selecione uma imobiliária"),
    contratoFileName: z.string().min(1, "Upload do contrato é obrigatório"),
    dataConsent: z.boolean().refine((val) => val === true, "Você deve aceitar os termos"),
  })

  const pixFormSchema = z.object({
    pixKey: z.string().min(1, "Chave PIX é obrigatória"),
    documentFile: z.string().min(1, "Documento com foto é obrigatório"),
    termsAccepted: z.boolean().refine((val) => val === true, "Você deve aceitar os termos"),
  })

  // --- Reducer ---
  const reducer = (state: AppState, action: Action): AppState => {
    switch (action.type) {
      case "SET_STEP":
        return { ...state, step: action.payload, error: "", success: "" }
      case "SET_LOADING":
        return { ...state, loading: action.payload.loading, loadingMsg: action.payload.msg, ...(action.payload.loading ? { error: "", success: "" } : {}) }
      case "SET_FORM_DATA":
        return { ...state, formData: { ...state.formData, ...action.payload }, error: "", success: "" }
      case "SET_EXTRACTED_DATA":
        return { ...state, extractedData: action.payload }
      case "SET_ERROR":
        return { ...state, error: action.payload, success: "" }
      case "SET_SUCCESS":
        return { ...state, success: action.payload, error: "" }
      case "SHOW_REJECT_DIALOG":
        return { ...state, showRejectDialog: action.payload }
      case "SET_DOCUMENT":
        return { ...state, documentFileName: action.payload }
      case "SET_AUTH_STEP":
        return { ...state, authStep: action.payload, error: "", success: "" }
      case "SET_IS_NEW_USER":
        return { ...state, isNewUser: action.payload }
      case "SET_VERIFICATION_CODE":
        return { ...state, verificationCode: action.payload }
      case "SET_AUTH_TOKEN":
        return { ...state, authToken: action.payload }
      case "SET_USER":
        return { ...state, user: action.payload }
      case "RESET_STATE":
        return INITIAL_STATE
      default:
        return state
    }
  }

  // --- Main Component State ---
  const [state, dispatch] = useReducer(reducer, INITIAL_STATE)
  const firstInputRef = useRef<HTMLInputElement>(null)
  const [showQuickNav, setShowQuickNav] = useState(false)

  // --- Helper Functions ---
  // Função para validar CPF matematicamente
  const validateCPF = useCallback((cpf: string): boolean => {
    const strCPF = cpf.replace(/\D/g, "")

    if (strCPF.length !== 11) return false

    // Verifica se todos os dígitos são iguais
    if ([
      '00000000000',
      '11111111111',
      '22222222222',
      '33333333333',
      '44444444444',
      '55555555555',
      '66666666666',
      '77777777777',
      '88888888888',
      '99999999999',
    ].indexOf(strCPF) !== -1) return false

    // Validação do primeiro dígito verificador
    let soma = 0
    for (let i = 1; i <= 9; i++) {
      soma = soma + parseInt(strCPF.substring(i - 1, i)) * (11 - i)
    }
    let resto = (soma * 10) % 11
    if ((resto === 10) || (resto === 11)) resto = 0
    if (resto !== parseInt(strCPF.substring(9, 10))) return false

    // Validação do segundo dígito verificador
    soma = 0
    for (let i = 1; i <= 10; i++) {
      soma = soma + parseInt(strCPF.substring(i - 1, i)) * (12 - i)
    }
    resto = (soma * 10) % 11
    if ((resto === 10) || (resto === 11)) resto = 0
    if (resto !== parseInt(strCPF.substring(10, 11))) return false

    return true
  }, [])

  const maskCPF = useCallback(
    (value: string) =>
      value
        .replace(/\D/g, "")
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d{1,2})/, "$1-$2")
        .replace(/(-\d{2})\d+?$/, "$1"),
    [],
  )

  const maskPhone = useCallback(
    (value: string) =>
      value
        .replace(/\D/g, "")
        .replace(/(\d{2})(\d)/, "($1) $2")
        .replace(/(\d{5})(\d)/, "$1-$2")
        .replace(/(-\d{4})\d+?$/, "$1"),
    [],
  )

  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value)

  const parseCurrency = (value: string): number => {
    return Number(value.replace(/[^\d,]/g, "").replace(",", "."))
  }

  const startLoading = useCallback((min: number, max: number, msg: string, onComplete: () => void) => {
    dispatch({ type: "SET_LOADING", payload: { loading: true, msg } })
    const duration = Math.random() * (max - min) + min
    setTimeout(() => {
      onComplete()
      dispatch({ type: "SET_LOADING", payload: { loading: false, msg: "" } })
      setTimeout(() => firstInputRef.current?.focus(), 100)
    }, duration)
  }, [])

  const handlePreviousStep = () => {
    if (state.step > 1) {
      dispatch({ type: "SET_STEP", payload: state.step - 1 })
    }
  }

  // --- Effects ---
  useEffect(() => {
    try {
      const savedState = localStorage.getItem("x-auth-state")
      if (savedState) {
        const parsed = JSON.parse(savedState)
        if (parsed.authToken) {
          window.location.href = "/dashboard"
          return
        }
        // We can't dispatch the entire state, so we'll handle this differently
        // For now, we'll skip the localStorage restoration to avoid complexity
      }
    } catch (e) {
      console.error("Failed to load state", e)
    }
  }, [])

  useEffect(() => {
    localStorage.setItem("locpay_app_state_v1", JSON.stringify(state))
  }, [state])

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }, [state.step])

  // --- Style Classes ---
  const commonInputClass =
    "h-12 bg-white border border-gray-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 rounded-lg text-sm text-gray-900 placeholder:text-gray-400 transition-all duration-200 shadow-sm"
  const inputWithIconClass = "pl-10 pr-4"
  const mainButtonClass =
    "w-full h-12 text-sm font-semibold bg-gradient-to-r from-[#0B4375] to-blue-700 hover:from-[#0B4375] hover:to-blue-800 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2"
  const greenButtonClass =
    "w-full h-12 text-sm font-semibold bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2"
  const redButtonClass =
    "w-full h-10 text-sm font-medium bg-red-50 border border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300 transition-all duration-200 rounded-lg"

  // --- Component Definitions ---
  const QuickNavigation = () => (
    <Card className="my-4 bg-white shadow-xl rounded-2xl border border-gray-200">
      <CardHeader className="pb-2 pt-4">
        <div className="flex justify-between items-center">
          <CardTitle className="text-sm text-[#0B4375] flex items-center font-bold">
            <Settings2 className="h-4 w-4 mr-2 text-[#0B4375]" />
            Navegação Rápida
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowQuickNav(false)}
            className="text-[#0B4375] hover:bg-blue-50 h-8 w-8 rounded-full"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pb-4">
        <div className="flex flex-wrap gap-2 justify-center mb-3">
          {[
            { step: 0, label: "Início" },
            { step: 1, label: "Cadastro" },
            { step: 2, label: "Validação" },
            { step: 3, label: "Proposta" },
            { step: 4, label: "Confirmação" },
            { step: 5, label: "Sucesso" },
          ].map((navItem) => (
            <Button
              key={navItem.step}
              onClick={() => dispatch({ type: "SET_STEP", payload: navItem.step })}
              variant={state.step === navItem.step ? "default" : "outline"}
              size="sm"
              className={`${state.step === navItem.step
                ? "bg-blue-600 hover:bg-blue-700 text-white text-xs font-bold"
                : "border-[#0B4375] text-[#0B4375] hover:bg-blue-50 text-xs font-medium"
                } rounded-lg transition-all duration-200`}
            >
              {navItem.label}
            </Button>
          ))}
        </div>
        <div className="border-t pt-3">
          <Button
            onClick={() => {
              dispatch({
                type: "SET_FORM_DATA",
                payload: {
                  nomeCompleto: "João da Silva Santos",
                  cpf: "123.456.789-00",
                  whatsapp: "(11) 99999-9999",
                  valorAluguelLiquido: "R$ 2.800,00",
                  mesesAntecipacao: "6",
                  imobiliaria: "Imobiliária Alfa Ltda",
                  contratoFileName: "contrato-mock.pdf",
                  jaEhCliente: false,
                  dataConsent: true,
                },
              })
              dispatch({ type: "SET_EXTRACTED_DATA", payload: MOCK_EXTRACTED_DATA })
            }}
            variant="outline"
            size="sm"
            className="w-full text-xs border-orange-300 text-orange-600 hover:bg-orange-50"
          >
            🚀 DEV: Preencher Dados Mock
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  const StepIndicator = () => {
    const stepsConfig = [
      { number: 1, label: "Cadastro" },
      { number: 2, label: "Validação" },
      { number: 3, label: "Proposta" },
      { number: 4, label: "Confirmação" },
    ]
    const currentStep = state.step

    if (currentStep === 0) return null

    return (
      <div className="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm border-t border-gray-200 py-4 px-4 z-40 shadow-lg">
        <div className="max-w-md mx-auto">
          <div className="flex items-center justify-between relative">
            <div className="absolute top-4 left-8 right-8 h-0.5 bg-gray-200 rounded-full"></div>
            <div
              className="absolute top-4 left-8 h-0.5 bg-gradient-to-r from-[#0B4375] to-[#0B4375] rounded-full transition-all duration-700 ease-out"
              style={{
                width: `${Math.max(0, ((currentStep - 1) / (stepsConfig.length - 1)) * 100)}%`,
                maxWidth: "calc(100% - 4rem)",
              }}
            ></div>

            {stepsConfig.map((stepItem, index) => (
              <div key={stepItem.number} className="flex flex-col items-center relative z-10">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold border-2 transition-all duration-500 ease-out ${currentStep > stepItem.number
                    ? "bg-gradient-to-r from-[#0B4375] to-[#0B4375] text-white border-[#0B4375] scale-110"
                    : currentStep === stepItem.number
                      ? "bg-gradient-to-r from-[#0B4375] to-[#0B4375] text-white border-[#0B4375] scale-110 shadow-lg"
                      : "bg-white text-gray-400 border-gray-300"
                    }`}
                >
                  {currentStep > stepItem.number ? <Check className="h-4 w-4" /> : stepItem.number}
                </div>
                <span
                  className={`text-xs mt-2 font-medium transition-all duration-300 ${currentStep >= stepItem.number ? "text-[#0B4375]" : "text-gray-400"
                    }`}
                >
                  {stepItem.label}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const LoadingScreen = () => (
    <div className="fixed inset-0 bg-gradient-to-br from-[#0B4375]/95 to-[#0B4375]/95 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="text-center p-8 bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl max-w-sm mx-4 border border-white/20">
        <div className="relative mb-8">
          <div className="w-20 h-20 border-4 border-gray-200 rounded-full animate-spin mx-auto">
            <div className="w-full h-full border-4 border-transparent border-t-blue-600 border-r-blue-700 rounded-full animate-spin"></div>
          </div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center shadow-lg">
              <Check className="w-5 h-5 text-white" />
            </div>
          </div>
        </div>
        <div className="space-y-4">
          <p className="text-[#0B4375] font-bold text-lg mb-2" aria-live="polite">
            {state.loadingMsg}
          </p>
        </div>
      </div>
    </div>
  )

  const PageHeader = ({ showBackArrow = true }: { showBackArrow?: boolean }) => {
    if (state.step === 0) return null

    return (
      <div className="flex items-center justify-between mb-6 px-4 pt-6">
        {showBackArrow && state.step > 1 ? (
          <Button
            variant="ghost"
            size="icon"
            onClick={handlePreviousStep}
            className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
            aria-label="Voltar para etapa anterior"
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>
        ) : (
          <div className="w-10 h-10" />
        )}
        <div className="flex justify-center">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 shadow-lg">
            <Image src="/images/locpay-logo.png" alt="LocPay" width={100} height={26} className="h-6 w-auto" />
          </div>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => dispatch({ type: "RESET_STATE" })}
          className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
          aria-label="Fechar"
        >
          <X className="h-5 w-5" />
        </Button>
      </div>
    )
  }

  const InputField = ({
    icon: Icon,
    id,
    placeholder,
    value,
    onChange,
    maxLength,
    type = "text",
    inputRef,
    label,
    error,
    hasError = false,
  }: {
    icon?: React.ComponentType<any>
    id: string
    placeholder: string
    value: string
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
    maxLength?: number
    type?: string
    inputRef?: React.RefObject<HTMLInputElement>
    label?: string
    error?: string
    hasError?: boolean
  }) => (
    <div className="space-y-2">
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}
      <div className="relative">
        {Icon && <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />}
        <Input
          ref={inputRef}
          id={id}
          type={type}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          maxLength={maxLength}
          aria-invalid={hasError}
          className={`${commonInputClass} ${Icon ? inputWithIconClass : "px-3"} ${hasError ? "border-red-500 focus:border-red-500" : ""
            }`}
        />
      </div>
      {error && <p className="text-sm text-red-600 mt-1">{error}</p>}
    </div>
  )

  const SecurityBadge = () => (
    <div className="flex items-center justify-center gap-2 mt-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
      <Lock className="w-4 h-4 text-gray-600" />
      <span className="text-sm text-gray-600">Seus dados estão protegidos com criptografia de ponta</span>
    </div>
  )

  const RejectDialog = () => (
    <Dialog
      open={state.showRejectDialog}
      onOpenChange={(open) => dispatch({ type: "SHOW_REJECT_DIALOG", payload: open })}
    >
      <DialogContent className="max-w-md mx-auto rounded-2xl border-0 shadow-2xl bg-gradient-to-br from-white to-gray-50">
        <DialogHeader className="text-center pb-6">
          <div className="flex justify-center mb-4">
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg p-3 shadow-lg">
              <Image src="/images/locpay-logo.png" alt="LocPay" width={80} height={21} className="h-5 w-auto" />
            </div>
          </div>
          <div className="relative w-16 h-16 mx-auto mb-6">
            <div className="absolute inset-0 bg-gradient-to-r from-red-100 to-orange-100 rounded-full"></div>
            <div className="relative w-full h-full bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center border-4 border-white shadow-xl">
              <ThumbsDown className="h-8 w-8 text-white" />
            </div>
          </div>
          <DialogTitle className="text-xl font-bold text-gray-800 mb-3">Que pena!</DialogTitle>
          <DialogDescription className="text-gray-600 text-sm leading-relaxed">
            Entendemos que nossa proposta não atendeu suas expectativas no momento.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="relative p-4 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-lg border border-blue-200 overflow-hidden">
            <div className="relative">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg flex items-center justify-center shadow-lg">
                  <TrendingUp className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h4 className="font-bold text-gray-800 text-base">Não desista!</h4>
                  <p className="text-sm text-gray-600">Nossas condições mudam constantemente</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  onClick={() => {
                    dispatch({ type: "RESET_STATE" })
                  }}
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg h-10 text-xs font-bold shadow-md hover:shadow-lg transition-all duration-200"
                >
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Nova Simulação
                </Button>
                <Button
                  onClick={() =>
                    window.open(
                      "https://wa.me/5541999999999?text=Olá! Gostaria de falar sobre as condições da antecipação de aluguel.",
                      "_blank",
                    )
                  }
                  className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white rounded-lg h-10 text-xs font-bold shadow-md hover:shadow-lg transition-all duration-200"
                >
                  <MessageCircle className="w-3 h-3 mr-1" />
                  WhatsApp
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )

  // --- Authentication Functions ---
  const handleCPFSubmit = async (cpf: string) => {
    dispatch({ type: "SET_LOADING", payload: { loading: true, msg: "Verificando CPF..." } })

    try {
      const response = await fetch("/api/v1/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cpf: cpf.replace(/\D/g, ""),
        }),
      })

      const result = await response.json()

      if (response.ok && result.codeSent) {
        // User exists, proceed to verification
        dispatch({ type: "SET_IS_NEW_USER", payload: false })
        dispatch({ type: "SET_AUTH_STEP", payload: "verification" })
        dispatch({ type: "SET_FORM_DATA", payload: { cpf } })
        dispatch({ type: "SET_SUCCESS", payload: "Código enviado para seu WhatsApp!" })
      } else if (response.status === 400 && result.message?.includes("Telefone e Nome é obrigatório")) {
        // New user, show registration fields
        dispatch({ type: "SET_IS_NEW_USER", payload: true })
        dispatch({ type: "SET_AUTH_STEP", payload: "registration" })
        dispatch({ type: "SET_FORM_DATA", payload: { cpf } })
      } else {
        dispatch({ type: "SET_ERROR", payload: result.message || "Erro ao verificar CPF" })
      }
    } catch (error) {
      console.error("Erro ao verificar CPF:", error)
      dispatch({ type: "SET_ERROR", payload: "Erro de conexão. Tente novamente." })
    } finally {
      dispatch({ type: "SET_LOADING", payload: { loading: false, msg: "" } })
    }
  }

  const handleRegistrationSubmit = async (data: { nomeCompleto: string; cpf: string; whatsapp: string }) => {
    dispatch({ type: "SET_LOADING", payload: { loading: true, msg: "Criando conta..." } })

    try {
      const response = await fetch("/api/v1/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: data.nomeCompleto,
          cpf: data.cpf.replace(/\D/g, ""),
          phone: data.whatsapp.replace(/\D/g, ""),
        }),
      })

      const result = await response.json()

      if (response.ok && result.codeSent) {
        dispatch({ type: "SET_AUTH_STEP", payload: "verification" })
        dispatch({ type: "SET_FORM_DATA", payload: data })
        dispatch({ type: "SET_SUCCESS", payload: "Conta criada com sucesso! Código enviado para seu WhatsApp." })
      } else {
        dispatch({ type: "SET_ERROR", payload: result.message || "Erro ao criar conta" })
      }
    } catch (error) {
      console.error("Erro ao criar conta:", error)
      dispatch({ type: "SET_ERROR", payload: "Erro de conexão. Tente novamente." })
    } finally {
      dispatch({ type: "SET_LOADING", payload: { loading: false, msg: "" } })
    }
  }

  const handleVerificationSubmit = async (code: string) => {
    dispatch({ type: "SET_LOADING", payload: { loading: true, msg: "Verificando código..." } })

    try {
      const response = await fetch("/api/v1/auth/verify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cpf: state.formData.cpf.replace(/\D/g, ""),
          code: code,
        }),
      })

      const result = await response.json()

      if (response.ok && result.accessToken) {
        dispatch({ type: "SET_AUTH_TOKEN", payload: result.accessToken })
        dispatch({ type: "SET_USER", payload: result.user })
        dispatch({ type: "SET_AUTH_STEP", payload: "complete" })
        dispatch({ type: "SET_SUCCESS", payload: "Código verificado com sucesso! Redirecionando..." })

        const authData = {
          authToken: result.accessToken,
          user: result.user,
        }

        localStorage.setItem("x-auth-state", JSON.stringify(authData))

        setTimeout(() => {
          window.location.href = "/dashboard"
        }, 1500)
      } else {
        dispatch({
          type: "SET_ERROR",
          payload: result.message || "Código inválido. Verifique o código enviado pelo WhatsApp."
        })
      }
    } catch (error) {
      console.error("Erro ao verificar código:", error)
      dispatch({
        type: "SET_ERROR",
        payload: "Erro de conexão. Tente novamente."
      })
    } finally {
      dispatch({ type: "SET_LOADING", payload: { loading: false, msg: "" } })
    }
  }

  const handleResendCode = async () => {
    dispatch({ type: "SET_LOADING", payload: { loading: true, msg: "Reenviando código..." } })

    try {
      const response = await fetch("/api/v1/auth/resend-code", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cpf: state.formData.cpf.replace(/\D/g, ""),
        }),
      })

      const result = await response.json()
      if (response.ok && result.codeSent) {
        dispatch({ type: "SET_SUCCESS", payload: "Código reenviado com sucesso! Verifique seu WhatsApp." })
      } else {
        dispatch({ type: "SET_ERROR", payload: result.message || "Erro ao reenviar código" })
      }
    } catch (error) {
      console.error("Erro ao reenviar código:", error)
      dispatch({ type: "SET_ERROR", payload: "Erro de conexão. Tente novamente." })
    } finally {
      dispatch({ type: "SET_LOADING", payload: { loading: false, msg: "" } })
    }
  }

  // --- Authentication Form Components ---
  const CPFForm = () => {
    const cpfForm = useForm<z.infer<typeof cpfSchema>>({
      resolver: zodResolver(cpfSchema),
      defaultValues: { cpf: state.formData.cpf },
    })

    const onCPFSubmit = (data: z.infer<typeof cpfSchema>) => {
      handleCPFSubmit(data.cpf)
    }

    return (
      <form onSubmit={cpfForm.handleSubmit(onCPFSubmit)} className="space-y-4">
        {state.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
            {state.error}
          </div>
        )}
        {state.success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm">
            {state.success}
          </div>
        )}
        <Controller
          name="cpf"
          control={cpfForm.control}
          render={({ field, fieldState }) => (
            <InputField
              icon={CreditCard}
              id="cpf"
              placeholder="000.000.000-00"
              value={field.value}
              onChange={(e) => field.onChange(maskCPF(e.target.value))}
              maxLength={14}
              error={fieldState.error?.message}
              hasError={!!fieldState.error}
            />
          )}
        />
        <Button type="submit" className={mainButtonClass} disabled={state.loading}>
          <CreditCard className="w-4 h-4" />
          {state.loading ? "Verificando..." : "Continuar"}
          <ArrowRight className="w-4 h-4" />
        </Button>
        <p className="text-xs text-gray-500 text-center">
          Digite seu CPF para fazer login ou criar uma conta
        </p>
      </form>
    )
  }

  const RegistrationForm = () => {
    const registrationForm = useForm<z.infer<typeof registrationSchema>>({
      resolver: zodResolver(registrationSchema),
      defaultValues: {
        cpf: state.formData.cpf,
        nomeCompleto: "",
        whatsapp: "",
      },
    })

    const onRegistrationSubmit = (data: z.infer<typeof registrationSchema>) => {
      handleRegistrationSubmit(data)
    }

    return (
      <form onSubmit={registrationForm.handleSubmit(onRegistrationSubmit)} className="space-y-4">
        {state.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
            {state.error}
          </div>
        )}
        {state.success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm">
            {state.success}
          </div>
        )}
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg text-blue-700 text-sm">
          <p>Complete seus dados para criar sua conta:</p>
        </div>
        <Controller
          name="nomeCompleto"
          control={registrationForm.control}
          render={({ field, fieldState }) => (
            <InputField
              icon={Smile}
              id="nomeCompleto"
              placeholder="Seu nome completo"
              value={field.value}
              onChange={field.onChange}
              error={fieldState.error?.message}
              hasError={!!fieldState.error}
            />
          )}
        />
        <Controller
          name="cpf"
          control={registrationForm.control}
          render={({ field, fieldState }) => (
            <InputField
              icon={CreditCard}
              id="cpf"
              placeholder="000.000.000-00"
              value={field.value}
              onChange={(e) => field.onChange(maskCPF(e.target.value))}
              maxLength={14}
              error={fieldState.error?.message}
              hasError={!!fieldState.error}
            />
          )}
        />
        <Controller
          name="whatsapp"
          control={registrationForm.control}
          render={({ field, fieldState }) => (
            <InputField
              icon={Smartphone}
              id="whatsapp"
              placeholder="(11) 99999-9999"
              value={field.value}
              onChange={(e) => field.onChange(maskPhone(e.target.value))}
              maxLength={15}
              error={fieldState.error?.message}
              hasError={!!fieldState.error}
            />
          )}
        />
        <Button type="submit" className={mainButtonClass} disabled={state.loading}>
          <Rocket className="w-4 h-4" />
          {state.loading ? "Criando conta..." : "Criar Conta"}
          <ArrowRight className="w-4 h-4" />
        </Button>
        <Button
          type="button"
          variant="ghost"
          onClick={() => dispatch({ type: "SET_AUTH_STEP", payload: "cpf" })}
          className="w-full text-sm text-gray-600"
        >
          ← Voltar
        </Button>
      </form>
    )
  }

  const VerificationForm = () => {
    const verificationForm = useForm<z.infer<typeof verificationSchema>>({
      resolver: zodResolver(verificationSchema),
      defaultValues: { code: "" },
    })

    const onVerificationSubmit = (data: z.infer<typeof verificationSchema>) => {
      handleVerificationSubmit(data.code)
    }

    return (
      <form onSubmit={verificationForm.handleSubmit(onVerificationSubmit)} className="space-y-4">
        {state.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
            {state.error}
          </div>
        )}
        {state.success && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm">
            {state.success}
          </div>
        )}
        <div className="text-center mb-4">
          <Smartphone className="w-12 h-12 text-blue-600 mx-auto mb-2" />
          <p className="text-sm font-medium text-gray-700">Código enviado via WhatsApp</p>
          <p className="text-xs text-gray-500">
            Verifique seu WhatsApp e digite o código de 6 dígitos
          </p>
        </div>
        <Controller
          name="code"
          control={verificationForm.control}
          render={({ field, fieldState }) => (
            <InputField
              icon={Lock}
              id="code"
              placeholder="000000"
              value={field.value}
              onChange={(e) => field.onChange(e.target.value.replace(/\D/g, ""))}
              maxLength={6}
              error={fieldState.error?.message}
              hasError={!!fieldState.error}
            />
          )}
        />
        <Button type="submit" className={mainButtonClass} disabled={state.loading}>
          <CheckCircle className="w-4 h-4" />
          {state.loading ? "Verificando..." : "Verificar Código"}
        </Button>
        <div className="flex justify-between">
          <Button
            type="button"
            variant="ghost"
            onClick={() => dispatch({ type: "SET_AUTH_STEP", payload: state.isNewUser ? "registration" : "cpf" })}
            className="text-sm text-gray-600"
          >
            ← Voltar
          </Button>
          <Button
            type="button"
            variant="ghost"
            onClick={handleResendCode}
            className="text-sm text-blue-600"
            disabled={state.loading}
          >
            Reenviar código
          </Button>
        </div>
      </form>
    )
  }

  const AuthCompleteForm = () => {
    setTimeout(() => {
      window.location.href = "/dashboard"
    }, 1500);

    return (
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
        <div>
          <h3 className="text-lg font-bold text-gray-800">Bem-vindo!</h3>
          <p className="text-sm text-gray-600">
            {state.isNewUser ? "Conta criada com sucesso!" : "Login realizado com sucesso!"}
          </p>
        </div>
        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-sm text-green-700">
            Redirecionando para a próxima etapa...
          </p>
        </div>
      </div>
    )
  }

  const LandingPage = () => {

    return (
      <section aria-label="Página inicial - Dados pessoais">
        <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] relative overflow-hidden">
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
            <div className="absolute bottom-40 right-16 w-40 h-40 bg-white rounded-full blur-3xl"></div>
          </div>
          <div className="relative z-10 px-4 py-8 flex flex-col min-h-screen">
            {/* Header */}
            <div className="flex justify-center mb-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 shadow-lg">
                <Image src="/images/locpay-logo.png" alt="LocPay" width={140} height={37} className="h-9 w-auto" />
              </div>
            </div>

            <div className="flex-1 flex flex-col justify-center text-center">
              {/* Hero Section */}
              <div className="mb-8">
                <div className="text-4xl mb-4">🚀</div>
                <h1 className="text-3xl font-bold text-white mb-4 leading-tight">
                  Realize seus Projetos com a Antecipação de Aluguel!
                </h1>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 mb-6 max-w-sm mx-auto">
                  <div className="flex items-center justify-center gap-2 text-white/90 text-sm">
                    <span>💰</span>
                    <span className="font-medium">
                      Preencha os dados e Antecipe até 12 meses de aluguel sem burocracia.
                    </span>
                  </div>
                </div>
                <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
                  <Clock className="w-4 h-4 text-white" />
                  <span className="text-white text-sm font-medium">Processo 100% digital</span>
                  <Zap className="w-4 h-4 text-yellow-300" />
                </div>
              </div>

              {/* Remove esta seção inteira:
              {/* Feature Cards */}
              {/*<div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8 px-4">*/}
              {/*  <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 border border-white/20 shadow-lg">*/}
              {/*    <div className="text-center">*/}
              {/*      <div className="w-12 h-12 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-3">*/}
              {/*        <Clock className="w-6 h-6 text-white" />*/}
              {/*      </div>*/}
              {/*      <h3 className="font-bold text-gray-800 text-sm mb-2">Aprovação Ultra Rápida</h3>*/}
              {/*      <p className="text-xs text-gray-600 mb-3">*/}
              {/*        Análise e aprovação em até <span className="text-cyan-600 font-semibold">1 hora</span>. Seu*/}
              {/*        dinheiro na conta no mesmo dia!*/}
              {/*      </p>*/}
              {/*      <div className="inline-flex items-center gap-1 bg-cyan-50 text-cyan-600 px-2 py-1 rounded-full text-xs font-medium">*/}
              {/*        <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>*/}
              {/*        Mais rápido do mercado*/}
              {/*      </div>*/}
              {/*    </div>*/}
              {/*  </div>*/}

              {/*  <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 border border-white/20 shadow-lg">*/}
              {/*    <div className="text-center">*/}
              {/*      <div className="w-12 h-12 bg-gradient-to-r from-[#1e3a8a] to-[#2563eb] rounded-2xl flex items-center justify-center mx-auto mb-3">*/}
              {/*        <Shield className="w-6 h-6 text-white" />*/}
              {/*      </div>*/}
              {/*      <h3 className="font-bold text-gray-800 text-sm mb-2">Máxima Segurança</h3>*/}
              {/*      <p className="text-xs text-gray-600 mb-3">*/}
              {/*        Dados protegidos com <span className="text-blue-600 font-semibold">criptografia avançada</span> e*/}
              {/*        certificação SSL*/}
              {/*      </p>*/}
              {/*      <div className="inline-flex items-center gap-1 bg-green-50 text-green-600 px-2 py-1 rounded-full text-xs font-medium">*/}
              {/*        <div className="w-2 h-2 bg-green-400 rounded-full"></div>*/}
              {/*        Certificado SSL*/}
              {/*      </div>*/}
              {/*    </div>*/}
              {/*  </div>*/}

              {/*  <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 border border-white/20 shadow-lg">*/}
              {/*    <div className="text-center">*/}
              {/*      <div className="w-12 h-12 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-3">*/}
              {/*        <Smartphone className="w-6 h-6 text-white" />*/}
              {/*      </div>*/}
              {/*      <h3 className="font-bold text-gray-800 text-sm mb-2">100% Digital</h3>*/}
              {/*      <p className="text-xs text-gray-600 mb-3">*/}
              {/*        Tudo online! <span className="text-cyan-600 font-semibold">Zero burocracia</span>, sem papelada ou*/}
              {/*        filas*/}
              {/*      </p>*/}
              {/*      <div className="inline-flex items-center gap-1 bg-blue-50 text-blue-600 px-2 py-1 rounded-full text-xs font-medium">*/}
              {/*        <div className="w-2 h-2 bg-blue-400 rounded-full"></div>*/}
              {/*        Sem sair de casa*/}
              {/*      </div>*/}
              {/*    </div>*/}
              {/*  </div>*/}
              {/*</div>*/}

              {/* Testimonial Pill */}
              <div className="mb-6">
                <div className="bg-cyan-50/80 backdrop-blur-sm rounded-full px-4 py-2 border border-cyan-200/50 shadow-lg max-w-sm mx-auto">
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-5 h-5 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">🚀</span>
                    </div>
                    <span className="text-sm font-semibold text-cyan-700">
                      Mais de 1.000 proprietários já anteciparam com a LocPay
                    </span>
                  </div>
                </div>
              </div>

              {/* Authentication Form */}
              <div className="mb-8">
                <Card className="bg-white/95 backdrop-blur-sm shadow-xl rounded-2xl border border-white/20 max-w-sm mx-auto">
                  <CardContent className="p-6">
                    {state.authStep === "cpf" && <CPFForm />}
                    {state.authStep === "registration" && <RegistrationForm />}
                    {state.authStep === "verification" && <VerificationForm />}
                    {state.authStep === "complete" && <AuthCompleteForm />}
                  </CardContent>
                </Card>
              </div>

              {/* Stats Pills */}
              <div className="flex flex-wrap justify-center gap-3 mb-8 px-4">
                <div className="bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 shadow-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-lg flex items-center justify-center">
                      <TrendingUp className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-sm font-semibold text-gray-700">+R$ 7M antecipados</span>
                  </div>
                </div>
                <div className="bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 shadow-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
                      <Clock className="w-3 h-3 text-white" />
                    </div>
                    <span className="text-sm font-semibold text-gray-700">Aprovação em 1 hora</span>
                  </div>
                </div>
                <div className="bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 shadow-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-gradient-to-r from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
                      <span className="text-white text-xs font-bold">%</span>
                    </div>
                    <span className="text-sm font-semibold text-gray-700">Melhores taxas do mercado</span>
                  </div>
                </div>
              </div>

              {/* Rating */}
              <div className="mb-6">
                <div className="flex justify-center gap-1 mb-2">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-300 fill-current" />
                  ))}
                </div>
                <p className="text-white/90 text-sm font-medium">Mais de 10.000 clientes satisfeitos</p>
              </div>
            </div>

            {/* Footer */}
            <div className="space-y-4">
              <div className="text-center">
                <Button
                  onClick={() =>
                    window.open(
                      "https://wa.me/5541999999999?text=Olá! Gostaria de saber mais sobre a antecipação de aluguel.",
                      "_blank",
                    )
                  }
                  variant="ghost"
                  className="text-white/80 hover:text-white hover:bg-white/10 text-sm border border-white/20 rounded-lg px-6 py-2 transition-all duration-200"
                >
                  <Phone className="w-4 h-4 mr-2" />
                  Falar com especialista
                </Button>
              </div>
              <div className="flex items-center justify-center gap-4 text-white/60 text-xs pt-4">
                <div className="flex items-center gap-1">
                  <ShieldCheck className="w-3 h-3" />
                  <span>SSL Seguro</span>
                </div>
                <div className="flex items-center gap-1">
                  <Verified className="w-3 h-3" />
                  <span>LGPD</span>
                </div>
                <div className="flex items-center gap-1">
                  <BadgeCheck className="w-3 h-3" />
                  <span>Certificado</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="fixed top-4 left-4 z-50">
          <Button
            onClick={() => setShowQuickNav(!showQuickNav)}
            variant="outline"
            size="sm"
            className="text-xs border-orange-300 text-orange-600 hover:bg-orange-50 bg-white/90 backdrop-blur-sm"
          >
            🚀 DEV
          </Button>
        </div>
      </section>
    )
  }

  const Page1_CadastroOperacao = () => {
    const secondPageForm = useForm<z.infer<typeof secondPageSchema>>({
      resolver: zodResolver(secondPageSchema),
      defaultValues: {
        valorAluguelLiquido: "",
        mesesAntecipacao: "",
        imobiliaria: "",
        contratoFileName: "",
        dataConsent: false,
      },
    })

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0]
      if (file && file.type === "application/pdf") {
        secondPageForm.setValue("contratoFileName", file.name)
      } else if (file) {
        dispatch({ type: "SET_ERROR", payload: "Por favor, envie um arquivo PDF." })
      } else {
        secondPageForm.setValue("contratoFileName", "")
      }
    }

    const onSecondPageSubmit = (data: z.infer<typeof secondPageSchema>) => {
      dispatch({
        type: "SET_FORM_DATA",
        payload: {
          valorAluguelLiquido: data.valorAluguelLiquido,
          mesesAntecipacao: data.mesesAntecipacao,
          imobiliaria: data.imobiliaria,
          contratoFileName: data.contratoFileName,
          dataConsent: data.dataConsent,
        },
      })
      dispatch({ type: "SET_SUCCESS", payload: "Dados salvos! Analisando contrato..." })
      startLoading(2000, 3500, "Analisando contrato com IA...", () => {
        dispatch({ type: "SET_EXTRACTED_DATA", payload: MOCK_EXTRACTED_DATA })
        dispatch({ type: "SET_STEP", payload: 2 })
      })
    }

    const maskCurrency = (value: string) => {
      const numericValue = value.replace(/\D/g, "")
      const formattedValue = (Number(numericValue) / 100).toLocaleString("pt-BR", {
        style: "currency",
        currency: "BRL",
      })
      return formattedValue
    }

    return (
      <section aria-label="Etapa 1: Cadastro da operação">
        <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] pb-24">
          <PageHeader />
          <div className="px-4">
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-white mb-2">Olá, {state.formData.nomeCompleto.split(" ")[0]}!</h1>
              <p className="text-white/90 text-sm">Agora vamos cadastrar os dados da sua operação de antecipação</p>
            </div>
            <Card className="bg-white shadow-xl rounded-2xl border-0 mb-6">
              <CardContent className="space-y-4 p-6">
                {state.error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                    {state.error}
                  </div>
                )}
                {state.success && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm">
                    {state.success}
                  </div>
                )}
                <form onSubmit={secondPageForm.handleSubmit(onSecondPageSubmit)} className="space-y-4">
                  <Controller
                    name="valorAluguelLiquido"
                    control={secondPageForm.control}
                    render={({ field, fieldState }) => (
                      <InputField
                        icon={DollarSign}
                        id="valorAluguelLiquido"
                        label="Valor do Aluguel Líquido (que você recebe)"
                        placeholder="R$ 0,00"
                        value={field.value}
                        onChange={(e) => {
                          const maskedValue = maskCurrency(e.target.value)
                          field.onChange(maskedValue)
                        }}
                        error={fieldState.error?.message}
                        hasError={!!fieldState.error}
                      />
                    )}
                  />

                  <Controller
                    name="mesesAntecipacao"
                    control={secondPageForm.control}
                    render={({ field, fieldState }) => (
                      <div className="space-y-2">
                        <label htmlFor="mesesAntecipacao" className="block text-sm font-medium text-gray-700">
                          Quantos meses deseja antecipar? <span className="text-gray-500">(máx. 12)</span>
                        </label>
                        <div className="relative">
                          <CalendarDays className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <Input
                            id="mesesAntecipacao"
                            type="number"
                            min="1"
                            max="12"
                            placeholder="Ex: 6"
                            value={field.value}
                            onChange={(e) => {
                              const value = e.target.value
                              if (value === "" || (Number(value) >= 1 && Number(value) <= 12)) {
                                field.onChange(value)
                              }
                            }}
                            className={`${commonInputClass} ${inputWithIconClass} ${fieldState.error ? "border-red-500 focus:border-red-500" : ""
                              }`}
                            aria-invalid={!!fieldState.error}
                          />
                        </div>
                        {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                      </div>
                    )}
                  />

                  <Controller
                    name="imobiliaria"
                    control={secondPageForm.control}
                    render={({ field, fieldState }) => (
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          Imobiliária <span className="text-red-500">*</span>
                        </label>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <SelectTrigger
                            className={`${commonInputClass} ${fieldState.error ? "border-red-500 focus:border-red-500" : ""
                              }`}
                          >
                            <SelectValue placeholder="Selecione a Imobiliária" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="imobiliaria-alfa">Imobiliária Alfa Ltda</SelectItem>
                            <SelectItem value="imobiliaria-beta">Imobiliária Beta S/A</SelectItem>
                            <SelectItem value="imobiliaria-gamma">Imobiliária Gamma Corp</SelectItem>
                            <SelectItem value="imobiliaria-delta">Imobiliária Delta & Cia</SelectItem>
                            <SelectItem value="outra">Outra</SelectItem>
                          </SelectContent>
                        </Select>
                        {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                      </div>
                    )}
                  />

                  <Controller
                    name="contratoFileName"
                    control={secondPageForm.control}
                    render={({ field, fieldState }) => (
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          📄 Contrato de Locação (PDF) <span className="text-red-500">*</span>
                        </label>
                        <label
                          htmlFor="contrato"
                          className={`flex flex-col items-center justify-center w-full h-16 border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200 ${field.value
                            ? "border-blue-500 bg-blue-50 text-blue-600"
                            : "border-gray-300 bg-gray-50 text-gray-500 hover:border-gray-400"
                            } ${fieldState.error ? "border-red-500" : ""}`}
                        >
                          <Upload className={`w-5 h-5 mb-1 ${field.value ? "text-blue-600" : "text-gray-400"}`} />
                          <span className="text-sm font-medium">{field.value || "Clique para fazer upload"}</span>
                          <span className="text-xs text-gray-400">Apenas PDF - até 20MB</span>
                        </label>
                        <Input id="contrato" type="file" accept=".pdf" onChange={handleFileChange} className="hidden" />
                        {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                      </div>
                    )}
                  />

                  <Controller
                    name="dataConsent"
                    control={secondPageForm.control}
                    render={({ field, fieldState }) => (
                      <div className="space-y-2">
                        <div className="flex items-start space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                          <Checkbox
                            id="dataConsent"
                            checked={field.value}
                            onCheckedChange={(checked) => field.onChange(Boolean(checked))}
                            className="mt-0.5 border-2 border-gray-400 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600 w-4 h-4"
                            aria-invalid={!!fieldState.error}
                          />
                          <label htmlFor="dataConsent" className="text-sm text-gray-700 cursor-pointer">
                            Autorizo o uso dos meus dados para análise da antecipação conforme nossa{" "}
                            <Button variant="link" className="p-0 h-auto text-blue-600 text-sm font-medium underline">
                              Política de Privacidade
                            </Button>
                            .
                          </label>
                        </div>
                        {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                      </div>
                    )}
                  />

                  <Button type="submit" className={mainButtonClass}>
                    SOLICITAR ANTECIPAÇÃO
                    <Zap className="w-4 h-4" />
                  </Button>
                </form>
              </CardContent>
            </Card>
            <SecurityBadge />
          </div>
          <StepIndicator />
        </div>
        <div className="fixed top-4 left-4 z-50">
          <Button
            onClick={() => setShowQuickNav(!showQuickNav)}
            variant="outline"
            size="sm"
            className="text-xs border-orange-300 text-orange-600 hover:bg-orange-50 bg-white/90 backdrop-blur-sm"
          >
            🚀 DEV
          </Button>
        </div>
      </section>
    )
  }

  const Page2_Validation = () => {
    const confirmData = () => {
      dispatch({ type: "SET_SUCCESS", payload: "Dados confirmados! Gerando proposta..." })
      startLoading(2000, 3500, "Gerando sua proposta personalizada...", () => {
        dispatch({ type: "SET_STEP", payload: 3 })
      })
    }

    const handleIncorrectData = () => {
      dispatch({ type: "SET_STEP", payload: 1 })
      dispatch({
        type: "SET_ERROR",
        payload: "Por favor, verifique seus dados ou entre em contato conosco pelo WhatsApp para assistência.",
      })
    }

    return (
      <section aria-label="Etapa 2: Validação dos dados">
        <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] pb-24">
          <PageHeader />
          <div className="px-4">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-white mb-2">Confirme os Dados Extraídos</h1>
              <p className="text-white/90">Verifique se as informações do contrato estão corretas</p>
            </div>

            <Card className="bg-white shadow-xl rounded-3xl border-0 mb-6">
              <CardHeader className="bg-gradient-to-r from-[#0B4375] to-[#0B4375] text-white p-4 rounded-t-3xl">
                <CardTitle className="text-lg font-bold text-center">Dados Extraídos do Contrato</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="divide-y divide-gray-100">
                  <div className="p-4 flex items-center gap-4">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <Smile className="h-5 w-5 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500 font-medium mb-1">Inquilino</p>
                      <p className="font-bold text-[#0B4375] text-lg">{state.formData.nomeCompleto}</p>
                    </div>
                  </div>
                  <div className="p-4 flex items-center gap-4">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <FileText className="h-5 w-5 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500 font-medium mb-1">Proprietário</p>
                      <p className="font-bold text-[#0B4375] text-lg">
                        {state.extractedData?.Proprietário || "Maria Oliveira Costa"}
                      </p>
                    </div>
                  </div>
                  <div className="p-4 flex items-center gap-4">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <MapPin className="h-5 w-5 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500 font-medium mb-1">Imóvel</p>
                      <p className="font-bold text-[#0B4375] text-lg">
                        {state.extractedData?.Imóvel || "Rua das Palmeiras, 456, São Paulo/SP"}
                      </p>
                    </div>
                  </div>
                  <div className="p-4 flex items-center gap-4">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <DollarSign className="h-5 w-5 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500 font-medium mb-1">Valor do Aluguel</p>
                      <p className="font-bold text-[#0B4375] text-lg">{state.formData.valorAluguelLiquido}</p>
                    </div>
                  </div>
                  <div className="p-4 flex items-center gap-4">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <Calendar className="h-5 w-5 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500 font-medium mb-1">Vigência do Contrato</p>
                      <p className="font-bold text-[#0B4375] text-lg">01/01/2024 - 31/12/2026</p>
                    </div>
                  </div>
                  <div className="p-4 flex items-center gap-4">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <Building className="h-5 w-5 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-500 font-medium mb-1">Imobiliária</p>
                      <p className="font-bold text-[#0B4375] text-lg">
                        {state.extractedData?.Imobiliária || "Imobiliária Alfa Ltda"}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            <div className="space-y-4">
              <Button onClick={confirmData} className={greenButtonClass}>
                <CheckCircle2 className="w-5 h-5 mr-2" />
                DADOS CORRETOS, VER PROPOSTA
              </Button>
              <Button onClick={handleIncorrectData} className={redButtonClass}>
                <XCircle className="w-4 h-4 mr-2" />
                Dados incorretos, corrigir
              </Button>
              <div className="flex items-center justify-center gap-2 mt-4">
                <span className="text-white/80 text-sm">Precisa de ajuda?</span>
                <Button
                  onClick={() =>
                    window.open(
                      "https://wa.me/5541999999999?text=Olá! Preciso de ajuda com a validação dos dados.",
                      "_blank",
                    )
                  }
                  variant="link"
                  className="text-blue-200 hover:text-blue-100 text-sm font-medium underline p-0 h-auto"
                >
                  <MessageCircle className="w-4 h-4 mr-1" />
                  Fale no WhatsApp
                </Button>
              </div>
            </div>
          </div>
          <StepIndicator />
        </div>
        <div className="fixed top-4 left-4 z-50">
          <Button
            onClick={() => setShowQuickNav(!showQuickNav)}
            variant="outline"
            size="sm"
            className="text-xs border-orange-300 text-orange-600 hover:bg-orange-50 bg-white/90 backdrop-blur-sm"
          >
            🚀 DEV
          </Button>
        </div>
      </section>
    )
  }

  const Page3_Proposal = () => {
    const monthlyRent = state.formData.valorAluguelLiquido ? parseCurrency(state.formData.valorAluguelLiquido) : 2800
    const totalRentValue = monthlyRent * Number(state.formData.mesesAntecipacao || 6)
    const netValue = totalRentValue * 0.975

    const handleRejectProposal = () => {
      dispatch({ type: "SHOW_REJECT_DIALOG", payload: true })
    }

    return (
      <section aria-label="Etapa 3: Proposta de antecipação">
        <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] pb-24">
          <PageHeader />
          <div className="px-4">
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-white mb-2">Temos uma Proposta!</h1>
              <p className="text-white/90 text-sm">
                Sua antecipação foi <span className="font-bold">pré-aprovada</span>!
              </p>
            </div>
            <Card className="bg-white shadow-xl rounded-2xl border-0 mb-6">
              <CardContent className="space-y-4 p-6">
                {state.error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm">
                    {state.error}
                  </div>
                )}
                {state.success && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm">
                    {state.success}
                  </div>
                )}
                <div className="text-center">
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-emerald-200 rounded-lg p-4 mb-4">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <Award className="h-5 w-5 text-emerald-600" />
                      <span className="text-emerald-700 font-semibold text-sm">Proposta Especial</span>
                    </div>
                    <p className="text-3xl font-bold text-emerald-800 mb-1">{formatCurrency(netValue)}</p>
                    <p className="text-emerald-600 font-medium text-sm">Valor líquido a receber</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <div className="p-3 bg-gradient-to-br from-slate-50 to-slate-100 rounded-lg border border-slate-200">
                    <div className="flex items-center gap-2 mb-2">
                      <DollarSign className="h-4 w-4 text-slate-600" />
                      <span className="text-xs text-slate-600 font-medium">Aluguel Mensal</span>
                    </div>
                    <div className="text-center">
                      <p className="text-lg font-bold text-blue-600">{formatCurrency(monthlyRent)}</p>
                    </div>
                  </div>
                  <div className="p-3 bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg border border-indigo-200">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="h-4 w-4 text-indigo-600" />
                      <span className="text-xs text-indigo-600 font-medium">Meses Antecipados</span>
                    </div>
                    <p className="text-lg font-bold text-center text-blue-600">
                      {state.formData.mesesAntecipacao} meses
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <div className="space-y-3">
              <Button
                onClick={() => {
                  dispatch({ type: "SET_SUCCESS", payload: "Proposta aceita! Preparando dados finais..." })
                  startLoading(1500, 2500, "Preparando confirmação...", () => {
                    dispatch({ type: "SET_STEP", payload: 4 })
                  })
                }}
                className={greenButtonClass}
              >
                <CheckCircle2 className="w-4 h-4" />
                SIM, ACEITO A PROPOSTA!
              </Button>
              <Button onClick={handleRejectProposal} className={redButtonClass}>
                <ThumbsDown className="w-4 h-4 mr-2" />
                Não, recusar proposta
              </Button>
            </div>
          </div>
        </div>
        <RejectDialog />
        <StepIndicator />
        <div className="fixed top-4 left-4 z-50">
          <Button
            onClick={() => setShowQuickNav(!showQuickNav)}
            variant="outline"
            size="sm"
            className="text-xs border-orange-300 text-orange-600 hover:bg-orange-50 bg-white/90 backdrop-blur-sm"
          >
            🚀 DEV
          </Button>
        </div>
      </section>
    )
  }

  const Page3_Confirmation = () => {
    const pixForm = useForm<z.infer<typeof pixFormSchema>>({
      resolver: zodResolver(pixFormSchema),
      defaultValues: {
        pixKey: "",
        documentFile: "",
        termsAccepted: false,
      },
    })

    const monthlyRent = state.formData.valorAluguelLiquido ? parseCurrency(state.formData.valorAluguelLiquido) : 2800
    const netValue = monthlyRent * Number(state.formData.mesesAntecipacao || 6) * 0.975

    const onSubmit = (data: z.infer<typeof pixFormSchema>) => {
      startLoading(2000, 3000, "Enviando solicitação para análise final...", () => {
        dispatch({ type: "SET_STEP", payload: 5 })
      })
    }

    const handleDocumentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0]
      if (file && (file.type === "image/jpeg" || file.type === "image/png" || file.type === "application/pdf")) {
        pixForm.setValue("documentFile", file.name)
        dispatch({ type: "SET_DOCUMENT", payload: file.name })
      } else if (file) {
        dispatch({ type: "SET_ERROR", payload: "Por favor, envie um arquivo JPG, PNG ou PDF." })
      } else {
        pixForm.setValue("documentFile", "")
        dispatch({ type: "SET_DOCUMENT", payload: "" })
      }
    }

    return (
      <section aria-label="Etapa 4: Confirmação e dados PIX">
        <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] pb-24">
          <PageHeader />
          <div className="px-4">
            <div className="text-center mb-6">
              <h1 className="text-xl font-bold text-white mb-2">Informe o seu PIX</h1>
              <p className="text-white/90 text-sm">Confirme os detalhes finais da operação</p>
            </div>
            <div className="space-y-4 mb-6">
              <Card className="bg-white shadow-xl rounded-2xl border-0">
                <CardContent className="p-6">
                  <form onSubmit={pixForm.handleSubmit(onSubmit)} className="space-y-4">
                    <Controller
                      name="pixKey"
                      control={pixForm.control}
                      render={({ field, fieldState }) => (
                        <InputField
                          id="pixKey"
                          label="Sua chave PIX"
                          placeholder="CPF, e-mail, telefone ou chave aleatória"
                          value={field.value}
                          onChange={(e) => {
                            field.onChange(e.target.value)
                          }}
                          error={fieldState.error?.message}
                          hasError={!!fieldState.error}
                        />
                      )}
                    />

                    <Controller
                      name="documentFile"
                      control={pixForm.control}
                      render={({ field, fieldState }) => (
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            Documento com Foto (RG, CNH)
                          </label>
                          <label
                            htmlFor="document"
                            className={`flex flex-col items-center justify-center w-full h-16 border-2 border-dashed rounded-lg cursor-pointer transition-all duration-200 ${field.value
                              ? "border-blue-500 bg-blue-50 text-blue-600"
                              : "border-gray-300 bg-gray-50 text-gray-500 hover:border-gray-400"
                              } ${fieldState.error ? "border-red-500" : ""}`}
                          >
                            <Upload className={`w-5 h-5 mb-1 ${field.value ? "text-blue-600" : "text-gray-400"}`} />
                            <span className="text-sm font-medium">
                              {field.value || "Clique para fazer upload do documento"}
                            </span>
                            <span className="text-xs text-gray-400">Apenas JPG, PNG ou PDF - até 5MB</span>
                          </label>
                          <Input
                            id="document"
                            type="file"
                            accept="image/jpeg, image/png, application/pdf"
                            onChange={handleDocumentChange}
                            className="hidden"
                          />
                          {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                        </div>
                      )}
                    />

                    <div className="p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-base font-bold text-blue-600 mb-1">Dia do Repasse</h3>
                          <p className="text-sm text-gray-600">
                            Todo dia <span className="font-bold text-blue-600">5</span> do mês
                          </p>
                        </div>
                        <CalendarDays className="h-6 w-6 text-blue-600" />
                      </div>
                    </div>

                    <Card className="bg-white shadow-lg rounded-lg border-0 overflow-hidden">
                      <CardHeader className="bg-gradient-to-r from-[#0B4375] to-[#0B4375] text-white p-4">
                        <CardTitle className="text-lg font-bold flex items-center gap-2">
                          <FileText className="h-5 w-5" />
                          Resumo da Operação
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-0">
                        <div className="divide-y divide-gray-100">
                          <div className="p-4 flex items-center gap-3">
                            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                              <MapPin className="h-5 w-5 text-blue-600" />
                            </div>
                            <div className="flex-1">
                              <p className="text-xs text-gray-500 font-medium">Imóvel</p>
                              <p className="font-semibold text-blue-600 text-sm">{state.extractedData?.Imóvel}</p>
                            </div>
                          </div>
                          <div className="p-4 flex items-center gap-3">
                            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                              <DollarSign className="h-5 w-5 text-green-600" />
                            </div>
                            <div className="flex-1">
                              <p className="text-xs text-gray-500 font-medium">Valor do Aluguel</p>
                              <p className="font-semibold text-blue-600 text-sm">{formatCurrency(monthlyRent)}</p>
                            </div>
                          </div>
                          <div className="p-4 flex items-center gap-3">
                            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                              <Calendar className="h-5 w-5 text-purple-600" />
                            </div>
                            <div className="flex-1">
                              <p className="text-xs text-gray-500 font-medium">Meses Antecipados</p>
                              <p className="font-semibold text-blue-600 text-sm">
                                {state.formData.mesesAntecipacao} meses
                              </p>
                            </div>
                          </div>
                          <div className="p-4 bg-gradient-to-r from-blue-50 to-green-50">
                            <div className="flex justify-between items-center">
                              <div>
                                <p className="text-gray-600 font-medium text-base">Valor líquido a receber</p>
                                <p className="text-xs text-gray-500 mt-1">Valor que será depositado na sua conta</p>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-blue-600 text-2xl">{formatCurrency(netValue)}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Controller
                      name="termsAccepted"
                      control={pixForm.control}
                      render={({ field, fieldState }) => (
                        <div className="space-y-2">
                          <div className="flex items-start space-x-3 p-3 bg-white border border-gray-200 rounded-lg">
                            <Checkbox
                              id="terms"
                              checked={field.value}
                              onCheckedChange={(checked) => {
                                field.onChange(Boolean(checked))
                              }}
                              className="mt-0.5 border-2 border-gray-400 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600 w-4 h-4"
                              aria-invalid={!!fieldState.error}
                            />
                            <label htmlFor="terms" className="text-sm text-gray-700 cursor-pointer">
                              Li e aceito os{" "}
                              <Button
                                variant="link"
                                className="p-0 h-auto text-blue-600 text-sm font-medium underline inline-flex items-center gap-1"
                              >
                                <Eye className="w-3 h-3" />
                                Termos de Cessão de Crédito
                              </Button>
                            </label>
                          </div>
                          {fieldState.error && <p className="text-sm text-red-600 mt-1">{fieldState.error.message}</p>}
                        </div>
                      )}
                    />

                    <Button type="submit" className={greenButtonClass}>
                      <CheckCircle2 className="w-4 h-4" />
                      CONFIRMAR ANTECIPAÇÃO
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
            <div className="text-center">
              <Button
                type="button"
                onClick={() => {
                  dispatch({ type: "SET_STEP", payload: 5 })
                }}
                variant="outline"
                className="text-xs border-orange-300 text-orange-600 hover:bg-orange-50 mb-4"
              >
                🚀 DEV: Pular para Sucesso
              </Button>
            </div>
          </div>
          <StepIndicator />
        </div>
        <div className="fixed top-4 left-4 z-50">
          <Button
            onClick={() => setShowQuickNav(!showQuickNav)}
            variant="outline"
            size="sm"
            className="text-xs border-orange-300 text-orange-600 hover:bg-orange-50 bg-white/90 backdrop-blur-sm"
          >
            🚀 DEV
          </Button>
        </div>
      </section>
    )
  }

  const SuccessScreen = () => (
    <section aria-label="Etapa 5: Confirmação de envio da solicitação">
      <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
          <div className="absolute bottom-40 right-16 w-40 h-40 bg-blue-300 rounded-full blur-3xl"></div>
        </div>
        <div className="relative z-10 flex flex-col items-center justify-center px-4 py-12 min-h-screen text-center">
          <div className="relative mb-8">
            <div className="w-24 h-24 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-2xl">
              <CheckCircle2 className="text-white h-12 w-12" />
            </div>
            <div className="absolute -top-2 -right-2 w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
          </div>
          <div className="space-y-6 mb-12">
            <h1 className="text-3xl font-bold text-white">Solicitação Enviada!</h1>
            <div className="max-w-md mx-auto space-y-4">
              <p className="text-lg text-white/90">
                Parabéns, <span className="font-bold text-blue-200">{state.formData.nomeCompleto.split(" ")[0]}</span>!
              </p>
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20">
                <p className="text-white/90 leading-relaxed text-sm">
                  Nossa equipe irá analisar sua solicitação. Uma vez aprovada, você receberá o{" "}
                  <span className="font-semibold text-blue-200">contrato para assinatura pelo WhatsApp</span> e o PIX
                  será enviado em até 12 horas úteis.
                </p>
              </div>
            </div>
          </div>
          <Card className="bg-white shadow-2xl rounded-2xl border-0 max-w-sm w-full mb-8 relative">
            <CardContent className="relative p-6 space-y-4">
              <div className="text-center">
                <div className="text-2xl mb-3">🤝</div>
                <h3 className="text-lg font-bold text-blue-600 border-b border-blue-100 pb-2">Próximos Passos</h3>
                <p className="text-sm text-gray-500 mt-2">Acompanhe o andamento da sua solicitação</p>
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                  <div className="w-6 h-6 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs font-bold">1</span>
                  </div>
                  <span className="text-gray-700 font-medium text-sm">Análise da equipe LocPay</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                  <div className="w-6 h-6 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs font-bold">2</span>
                  </div>
                  <span className="text-gray-700 font-medium text-sm">Contrato enviado por WhatsApp</span>
                </div>
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <CheckCircle2 className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-gray-700 font-medium text-sm">PIX em até 12h após assinatura</span>
                </div>
              </div>
            </CardContent>
          </Card>
          <div className="space-y-3 w-full max-w-sm mb-8">
            <Button
              onClick={() =>
                window.open(
                  "https://wa.me/5541999999999?text=Olá! Acabei de enviar minha solicitação de antecipação.",
                  "_blank",
                )
              }
              className="w-full bg-green-600 hover:bg-green-700 text-white rounded-lg h-12 transition-all duration-200 flex items-center justify-center gap-2"
            >
              <MessageCircle className="w-4 h-4" />
              Acompanhar pelo WhatsApp
            </Button>
            <Button
              onClick={() => {
                localStorage.removeItem("locpay_app_state_v1")
                dispatch({ type: "RESET_STATE" })
              }}
              variant="ghost"
              className="w-full text-white/80 hover:text-white hover:bg-white/10 rounded-lg h-10 transition-all duration-200 border border-white/20"
            >
              Fazer nova solicitação
            </Button>
          </div>
          <div className="flex items-center justify-center gap-6 text-white text-sm font-medium">
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                <Shield className="w-3 h-3 text-white" />
              </div>
              <span>Dados Seguros</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                <CheckCircle className="w-3 h-3 text-white" />
              </div>
              <span>LGPD</span>
            </div>
          </div>
        </div>
        <div className="fixed top-4 left-4 z-50">
          <Button
            onClick={() => setShowQuickNav(!showQuickNav)}
            variant="outline"
            size="sm"
            className="text-xs border-orange-300 text-orange-600 hover:bg-orange-50 bg-white/90 backdrop-blur-sm"
          >
            🚀 DEV
          </Button>
        </div>
      </div>
    </section>
  )

  const renderStep = () => {
    switch (state.step) {
      case 0:
        return <LandingPage />
      case 1:
        return <Page1_CadastroOperacao />
      case 2:
        return <Page2_Validation />
      case 3:
        return <Page3_Proposal />
      case 4:
        return <Page3_Confirmation />
      case 5:
        return <SuccessScreen />
      default:
        return <LandingPage />
    }
  }

  return (
    <main>
      <div className="relative">
        {state.loading && <LoadingScreen />}
        {showQuickNav && <QuickNavigation />}
        {renderStep()}
        <Button
          onClick={() => setShowQuickNav(!showQuickNav)}
          className="fixed bottom-4 right-4 w-12 h-12 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-2xl z-50 sm:hidden"
          size="icon"
        >
          <Settings2 className="w-5 h-5" />
        </Button>
      </div>
    </main>
  )
}
